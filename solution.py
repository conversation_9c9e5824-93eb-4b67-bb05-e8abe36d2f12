import torch
import torch.nn as nn
import torch.nn.functional as F


class ChannelAttention(nn.Module):
    """信道注意力模块 - 专门针对MIMO信道特征"""
    def __init__(self, in_channels: int, reduction: int = 8):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        b, c, _, _ = x.size()

        # 全局平均池化和最大池化
        avg_out = self.fc(self.avg_pool(x).view(b, c))
        max_out = self.fc(self.max_pool(x).view(b, c))
        
        # 注意力权重
        attention = self.sigmoid(avg_out + max_out).view(b, c, 1, 1)
        
        return x * attention


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, kernel_size: int = 7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # 通道维度的平均和最大
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        
        # 拼接并卷积
        attention = torch.cat([avg_out, max_out], dim=1)
        attention = self.sigmoid(self.conv(attention))
        
        return x * attention


class EfficientFeatureExtractor(nn.Module):
    """高效特征提取网络 - 使用注意力机制和深度可分离卷积"""
    def __init__(self, input_channels: int = 2):
        super().__init__()
        
        # 第一层：标准卷积
        self.conv1 = nn.Sequential(
            nn.Conv2d(input_channels, 32, 3, padding=1, bias=False),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )
        self.ca1 = ChannelAttention(32)
        
        # 第二层：深度可分离卷积
        self.depthwise2 = nn.Conv2d(32, 32, 3, padding=1, groups=32, bias=False)
        self.pointwise2 = nn.Conv2d(32, 64, 1, bias=False)
        self.bn2 = nn.BatchNorm2d(64)
        self.ca2 = ChannelAttention(64)
        self.sa2 = SpatialAttention()
        
        # 第三层：深度可分离卷积
        self.depthwise3 = nn.Conv2d(64, 64, 3, padding=1, groups=64, bias=False)
        self.pointwise3 = nn.Conv2d(64, 128, 1, bias=False)
        self.bn3 = nn.BatchNorm2d(128)
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool2d((2, 2))
        
        # 输出维度
        self.output_dim = 128 * 2 * 2
    
    def forward(self, x):
        # 第一层
        x = self.conv1(x)
        x = self.ca1(x)
        
        # 第二层
        x = self.depthwise2(x)
        x = self.pointwise2(x)
        x = self.bn2(x)
        x = F.relu(x, inplace=True)
        x = self.ca2(x)
        x = self.sa2(x)
        
        # 第三层
        x = self.depthwise3(x)
        x = self.pointwise3(x)
        x = self.bn3(x)
        x = F.relu(x, inplace=True)
        
        # 全局池化
        x = self.global_pool(x)
        x = x.view(x.size(0), -1)
        
        return x


class StableOrthogonalLayer(nn.Module):
    """稳定的正交化层 - 改进的Gram-Schmidt算法"""
    def __init__(self, eps: float = 1e-8):
        super().__init__()
        self.eps = eps
    
    def forward(self, x):
        # x: [batch, dim1, dim2, 2] where last dim is [real, imag]
        _, _, dim2, _ = x.shape

        # 转换为复数
        x_complex = torch.complex(x[..., 0], x[..., 1])

        # 改进的Gram-Schmidt正交化 - 避免原地操作
        Q_list = []

        for i in range(dim2):
            # 获取当前列
            v = x_complex[:, :, i]

            # 减去前面所有列的投影
            for j in range(i):
                q_j = Q_list[j]
                # 计算投影系数 <q_j, v>
                proj_coeff = torch.sum(q_j.conj() * v, dim=1, keepdim=True)
                # 减去投影 - 创建新张量
                v = v - proj_coeff * q_j

            # 归一化 - 创建新张量
            norm = torch.norm(v, dim=1, keepdim=True)
            norm = torch.clamp(norm, min=self.eps)
            q_normalized = v / norm

            Q_list.append(q_normalized)

        # 堆叠所有列
        Q = torch.stack(Q_list, dim=2)

        # 转换回实虚部表示
        result = torch.stack([Q.real, Q.imag], dim=-1)
        return result


class CompactUnitaryBranch(nn.Module):
    """紧凑的酉矩阵分支网络 - 减少参数和FLOPs"""
    def __init__(self, input_dim: int, output_dim: int, rank: int):
        super().__init__()
        self.output_dim = output_dim
        self.rank = rank
        
        # 使用更紧凑的网络结构
        hidden_dim = min(128, max(64, (output_dim * rank) // 4))
        
        self.feature_net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, output_dim * rank * 2)
        )
        
        # 正交化层
        self.orthogonal = StableOrthogonalLayer()
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        batch_size = x.shape[0]
        
        # 生成未正交化的矩阵
        out = self.feature_net(x)
        out = out.reshape(batch_size, self.output_dim, self.rank, 2)
        
        # 应用正交化
        out = self.orthogonal(out)
        
        return out


class AdaptiveSingularValueBranch(nn.Module):
    """自适应奇异值分支网络"""
    def __init__(self, input_dim: int, rank: int):
        super().__init__()
        self.rank = rank
        
        # 主网络
        self.net = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(64, rank)
        )
        
        # 自适应缩放因子
        self.scale_net = nn.Sequential(
            nn.Linear(input_dim, 32),
            nn.ReLU(inplace=True),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        # 基础奇异值
        s = self.net(x)
        s = F.softplus(s)  # 确保为正
        
        # 自适应缩放
        scale = self.scale_net(x) * 2.0 + 0.5  # 缩放到[0.5, 2.5]
        s = s * scale
        
        # 按降序排列
        s, _ = torch.sort(s, dim=1, descending=True)
        
        return s


class EnhancedLightweightExtractor(nn.Module):
    """增强轻量特征提取 - 平衡FLOPs和精度"""
    def __init__(self, input_channels: int = 2):
        super().__init__()

        # 两层深度可分离卷积，增加表达能力
        self.depthwise1 = nn.Conv2d(input_channels, input_channels, 3, padding=1, groups=input_channels, bias=False)
        self.pointwise1 = nn.Conv2d(input_channels, 24, 1, bias=False)
        self.bn1 = nn.BatchNorm2d(24)

        self.depthwise2 = nn.Conv2d(24, 24, 3, padding=1, groups=24, bias=False)
        self.pointwise2 = nn.Conv2d(24, 48, 1, bias=False)
        self.bn2 = nn.BatchNorm2d(48)

        # 全局池化 + 局部特征
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
        self.local_pool = nn.AdaptiveAvgPool2d((2, 2))

        # 特征融合
        self.feature_fusion = nn.Linear(48 + 48*4, 64)

        # 输出维度
        self.output_dim = 64

    def forward(self, x):
        # 第一层
        x = self.depthwise1(x)
        x = self.pointwise1(x)
        x = self.bn1(x)
        x = F.relu(x, inplace=True)

        # 第二层
        x = self.depthwise2(x)
        x = self.pointwise2(x)
        x = self.bn2(x)
        x = F.relu(x, inplace=True)

        # 全局和局部特征
        global_feat = self.global_pool(x).view(x.size(0), -1)
        local_feat = self.local_pool(x).view(x.size(0), -1)

        # 特征融合
        combined = torch.cat([global_feat, local_feat], dim=1)
        output = self.feature_fusion(combined)
        output = F.relu(output, inplace=True)

        return output


class FastOrthogonalLayer(nn.Module):
    """快速正交化层 - 避免原地操作"""
    def __init__(self, eps: float = 1e-6):
        super().__init__()
        self.eps = eps

    def forward(self, x):
        # x: [batch, dim1, dim2, 2] where last dim is [real, imag]
        _, _, dim2, _ = x.shape

        # 转换为复数
        x_complex = torch.complex(x[..., 0], x[..., 1])

        # 创建新的输出张量，避免原地操作
        Q_list = []

        for i in range(dim2):
            # 获取当前列
            v = x_complex[:, :, i].clone()  # 克隆避免原地操作

            # 只与前面少数几列正交化，减少计算量
            max_ortho = min(i, 4)  # 最多与前4列正交化
            for j in range(max_ortho):
                q_j = Q_list[j]
                # 计算投影系数
                proj_coeff = torch.sum(q_j.conj() * v, dim=1, keepdim=True)
                # 减去投影 - 创建新张量
                v = v - proj_coeff * q_j

            # 归一化
            norm = torch.norm(v, dim=1, keepdim=True)
            norm = torch.clamp(norm, min=self.eps)
            q_normalized = v / norm

            Q_list.append(q_normalized)

        # 堆叠所有列
        Q = torch.stack(Q_list, dim=2)

        # 转换回实虚部表示
        result = torch.stack([Q.real, Q.imag], dim=-1)
        return result


class EnhancedCompactBranch(nn.Module):
    """增强紧凑分支 - 平衡精度和效率"""
    def __init__(self, input_dim: int, output_dim: int, rank: int):
        super().__init__()
        self.output_dim = output_dim
        self.rank = rank

        # 增加网络深度，提高表达能力
        hidden_dim = max(48, min(96, (output_dim * rank) // 6))

        self.net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim // 2, output_dim * rank * 2)
        )

        # 改进的正交化
        self.orthogonal = FastOrthogonalLayer()

        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)  # 稍大的初始化
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        batch_size = x.shape[0]
        out = self.net(x)
        out = out.reshape(batch_size, self.output_dim, self.rank, 2)
        out = self.orthogonal(out)
        return out


class SimpleSingularBranch(nn.Module):
    """简化奇异值分支"""
    def __init__(self, input_dim: int, rank: int):
        super().__init__()
        self.rank = rank

        self.net = nn.Sequential(
            nn.Linear(input_dim, 32),
            nn.ReLU(inplace=True),
            nn.Linear(32, rank)
        )

        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.5)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        s = self.net(x)
        s = F.softplus(s) + 1e-6  # 确保为正
        s, _ = torch.sort(s, dim=1, descending=True)  # 降序排列
        return s


class LightweightSVDNet(nn.Module):
    """超轻量SVD网络 - 专为竞赛优化"""
    def __init__(self, M: int = 64, N: int = 64, R: int = 32):
        super().__init__()
        self.M = M
        self.N = N
        self.R = R

        # 增强轻量特征提取
        self.feature_extractor = EnhancedLightweightExtractor(input_channels=2)
        feature_dim = self.feature_extractor.output_dim

        # 增强紧凑分支网络
        self.u_branch = EnhancedCompactBranch(feature_dim, M, R)
        self.s_branch = SimpleSingularBranch(feature_dim, R)
        self.v_branch = EnhancedCompactBranch(feature_dim, N, R)

        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu', a=0.1)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)

    def forward(self, x):
        # x: [batch, M, N, 2]
        x = x.permute(0, 3, 1, 2).contiguous()

        # 轻量特征提取
        features = self.feature_extractor(x)

        # 分支计算
        U = self.u_branch(features)  # [batch, M, R, 2]
        s = self.s_branch(features)  # [batch, R]
        V = self.v_branch(features)  # [batch, N, R, 2]

        return U, s, V


class SVDNet(nn.Module):
    def __init__(self, M: int = 64, N: int = 64, R: int = 32):
        super().__init__()
        self.M = M
        self.N = N
        self.R = R

        # 高效特征提取网络
        self.feature_extractor = EfficientFeatureExtractor(input_channels=2)
        feature_dim = self.feature_extractor.output_dim

        # 分支网络
        self.u_branch = CompactUnitaryBranch(feature_dim, M, R)
        self.s_branch = AdaptiveSingularValueBranch(feature_dim, R)
        self.v_branch = CompactUnitaryBranch(feature_dim, N, R)

        # 初始化
        self._init_weights()

    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)

    def forward(self, x):
        # x: [batch, M, N, 2]
        # 调整维度用于卷积: [batch, 2, M, N]
        x = x.permute(0, 3, 1, 2).contiguous()

        # 特征提取
        features = self.feature_extractor(x)

        # 分支计算
        U = self.u_branch(features)  # [batch, M, R, 2]
        s = self.s_branch(features)  # [batch, R]
        V = self.v_branch(features)  # [batch, N, R, 2]

        return U, s, V


class OptimizedSVDNet(nn.Module):
    """优化的SVD网络接口 - 兼容现有代码"""
    def __init__(self, dim: int = 64, rank: int = 32):
        super().__init__()
        self.dim = dim
        self.rank = rank
        
        # 使用改进的SVD网络
        self.svd_net = SVDNet(dim, dim, rank)
    
    def forward(self, x):
        # 确保输入是4D张量
        if x.dim() == 3:
            x = x.unsqueeze(0)  # 添加batch维度
        
        # 使用改进的SVD网络
        U, S, V = self.svd_net(x)
        
        # 如果输入是单个样本，去掉batch维度
        if x.shape[0] == 1:
            U = U.squeeze(0)  # [M, R, 2]
            S = S.squeeze(0)  # [R]
            V = V.squeeze(0)  # [N, R, 2]
        
        return U, S, V


def count_lightweight_model_flops(M: int, N: int, R: int) -> int:
    """计算轻量级模型的FLOPs - 大幅优化"""
    # 轻量特征提取网络FLOPs
    depthwise_flops = 2 * 3 * 3 * M * N  # 深度卷积 (groups=2)
    pointwise_flops = 2 * 32 * M * N     # 点卷积
    pool_flops = 32  # 全局池化

    feature_dim = 32

    # 超紧凑分支网络FLOPs
    u_hidden = max(32, min(64, (M * R) // 8))  # 更小的隐藏层
    v_hidden = max(32, min(64, (N * R) // 8))

    u_branch_flops = feature_dim * u_hidden + u_hidden * (M * R * 2)
    s_branch_flops = feature_dim * 32 + 32 * R  # 简化的奇异值分支
    v_branch_flops = feature_dim * v_hidden + v_hidden * (N * R * 2)

    # 快速正交化FLOPs (大幅减少)
    # 只对前8列做完整正交化，其余只做归一化
    ortho_u_flops = M * min(8, R) * 4 + M * max(0, R-8) * 2  # 简化正交化
    ortho_v_flops = N * min(8, R) * 4 + N * max(0, R-8) * 2

    total_flops = (depthwise_flops + pointwise_flops + pool_flops +
                   u_branch_flops + s_branch_flops + v_branch_flops +
                   ortho_u_flops + ortho_v_flops)

    return total_flops


def count_model_flops(M: int, N: int, R: int) -> int:
    """计算改进模型的FLOPs"""
    # 特征提取网络FLOPs (更高效的设计)
    conv1_flops = 2 * 3 * 3 * 32 * M * N  # 第一层卷积

    # 深度可分离卷积FLOPs
    depthwise2_flops = 32 * 3 * 3 * M * N  # 深度卷积
    pointwise2_flops = 32 * 64 * M * N     # 点卷积

    depthwise3_flops = 64 * 3 * 3 * M * N  # 深度卷积
    pointwise3_flops = 64 * 128 * M * N    # 点卷积

    # 注意力机制FLOPs (相对较小)
    attention_flops = (32 + 64) * 16 + 128 * 8  # 简化估算

    # 全局池化
    pool_flops = 128 * 2 * 2

    feature_dim = 128 * 2 * 2

    # 分支网络FLOPs (更紧凑)
    u_hidden = min(128, max(64, (M * R) // 4))
    v_hidden = min(128, max(64, (N * R) // 4))

    u_branch_flops = feature_dim * u_hidden + u_hidden * (M * R * 2)
    s_branch_flops = feature_dim * 64 + 64 * R + feature_dim * 32 + 32 * 1
    v_branch_flops = feature_dim * v_hidden + v_hidden * (N * R * 2)

    # 正交化FLOPs (优化后)
    ortho_u_flops = M * R * R * 6  # 改进的Gram-Schmidt
    ortho_v_flops = N * R * R * 6

    total_flops = (conv1_flops + depthwise2_flops + pointwise2_flops +
                   depthwise3_flops + pointwise3_flops + attention_flops + pool_flops +
                   u_branch_flops + s_branch_flops + v_branch_flops +
                   ortho_u_flops + ortho_v_flops)

    return total_flops


def svd_loss(U, s, V, H_label, lambda_ortho=1.0, lambda_recon=1.0,
                     lambda_singular=0.1, lambda_smooth=0.05):
    """
    改进的SVD损失函数

    Args:
        U, s, V: 模型输出
        H_label: 标签信道矩阵
        lambda_ortho: 正交性约束权重
        lambda_recon: 重构损失权重
        lambda_singular: 奇异值约束权重
        lambda_smooth: 平滑性约束权重
    """
    from utils import complex_matmul, complex_transpose_conj

    batch_size = U.shape[0]
    device = U.device

    # 1. 重构损失 (使用更稳定的计算)
    Sigma = torch.zeros(batch_size, U.shape[2], V.shape[2], 2, device=device)
    for i in range(min(U.shape[2], V.shape[2])):
        Sigma[:, i, i, 0] = s[:, i]

    US = complex_matmul(U, Sigma)
    VH = complex_transpose_conj(V)
    H_recon = complex_matmul(US, VH)

    # 归一化重构损失
    recon_diff = H_recon - H_label
    recon_loss = torch.norm(recon_diff.view(batch_size, -1), dim=1) / \
                 (torch.norm(H_label.view(batch_size, -1), dim=1) + 1e-8)
    recon_loss = recon_loss.mean()

    # 2. 正交性约束损失 (改进的计算)
    UH = complex_transpose_conj(U)
    UHU = complex_matmul(UH, U)
    I_U = torch.zeros_like(UHU)
    for i in range(U.shape[2]):
        I_U[:, i, i, 0] = 1.0

    VH = complex_transpose_conj(V)
    VHV = complex_matmul(VH, V)
    I_V = torch.zeros_like(VHV)
    for i in range(V.shape[2]):
        I_V[:, i, i, 0] = 1.0

    ortho_loss_U = F.mse_loss(UHU, I_U)
    ortho_loss_V = F.mse_loss(VHV, I_V)
    ortho_loss = ortho_loss_U + ortho_loss_V

    # 3. 奇异值约束损失 (确保降序排列)
    s_diff = s[:, :-1] - s[:, 1:]  # 相邻奇异值差
    singular_loss = F.relu(-s_diff).mean()  # 惩罚非降序

    # 4. 平滑性约束 (减少过拟合)
    if U.shape[2] > 1:  # 确保有足够的维度进行差分
        smooth_loss_U = torch.mean(torch.norm(U[:, :, 1:] - U[:, :, :-1], dim=-1))
        smooth_loss_V = torch.mean(torch.norm(V[:, :, 1:] - V[:, :, :-1], dim=-1))
        smooth_loss = smooth_loss_U + smooth_loss_V
    else:
        smooth_loss = torch.tensor(0.0, device=device)

    # 总损失
    total_loss = (lambda_recon * recon_loss +
                  lambda_ortho * ortho_loss +
                  lambda_singular * singular_loss +
                  lambda_smooth * smooth_loss)

    return total_loss, recon_loss, ortho_loss_U, ortho_loss_V


# 为了向后兼容，提供别名
OptimizedSVDNet = SVDNet
RobustSVDNet = SVDNet
