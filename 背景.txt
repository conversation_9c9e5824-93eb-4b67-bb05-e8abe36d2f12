<一>科学问题
鼓励参赛选手充分发挥神经网络强大的非线性拟合能力，通过数据驱动方式设计面向无线信道的SVD算子，从而实现对传统SVD算法的高效替代。与此同时，考虑到实际系统需求，大赛也强调算法在复杂无线环境中的鲁棒性，即模型需具备良好的泛化能力，能够有效应对实际信道中的各种非理想因素，例如噪声、硬件非理性因素、信道估计误差等。不可避免地，AI使能的无线鲁棒SVD算子设计面临诸多技术挑战：
(1)酉矩阵的结构性保证：常规神经网络的输出通常不具备明确的结构性，而SVD输出的左右奇异矩阵必须满足正交性约束。为此，参赛者可考虑在神经网络设计中引入专有的酉矩阵生成模块，以确保输出矩阵满足正交性结构，但同时需保证梯度回传的稳定性。另一种方法是在损失函数中加入正交性正则项，通过惩罚项引导网络输出满足正交性结构。
(2)神经网络结构设计：神经网络结构对模型性能和效率具有决定性影响。
(3)模型的鲁棒性和泛化能力：考虑到实际通信环境的多样性，模型算法不仅需具备对各类噪声、信道估计误差等非理想因素的鲁棒性，还应能广泛适应不同的信道类型（如LoS、NLoS）和天线配置（如不同发射/接收天线维度组合）。


<二>任务描述
本次任务挑战无线信道SVD模型算法的准确性和鲁棒性。组委会提供了单小区多个场景下多个采样点位的非理想MIMO信道数据，其中部分数据同时包含其对应的理想信道标签。参赛队伍需根据提供的数据设计神经网络模型算法，建立输入（固定采样点位下的非理想信道）与输出（信道SVD近似结果）的功能模块。	

本赛题的核心任务是设计面向无线信道的鲁棒SVD模型算法，任务细节如下：
(1)算法输入：多个采样点位上的非理想信道，记为H∈R^(N_samp×M×N×2)
其中N_samp表示信道采样点数，M表示信道矩阵行数，N表示信道矩阵列数，2表示实虚部。
(2)算法输出：输出前R（R≤M<N）个最大的奇异值及其对应的左右奇异值向量，包括：
1)左奇异矩阵U∈R^(N_samp×M×R×2)(其中最后一个维度表示实虚部)；
2)奇异值向量s∈R^(N_samp×R)；
3)右奇异矩阵V∈R^(N_samp×N×R×2)(其中最后一个维度表示实虚部)。
(3)算法设计：参赛队伍根据组委会提供的数据集设计神经网络结构与训练策略，建立算法输入与输出的功能模块。
(4)设计目标：
1)模型输出应能够准确重构输入对应的理想信道矩阵H ̅_label∈C^(N_samp×M×N)，即U ̅[i,:,:]Σ[i,:,:] 〖V ̅[i,:,:]〗^*≈H ̅_label [i,:,:],i=1,…,N_samp
其中Σ[i,:,:]=diag(s[i,:]  )∈C^(R×R)，U ̅=U[:,:,:,0]+j*U[:,:,:,1]，V ̅=V[:,:,:,0]+j*V[:,:,:,1]
2)模型输出的左右奇异矩阵应满足正交性约束，即〖U ̅[i,:,:]〗^* U ̅[i,:,:]≈I,〖 V ̅[i,:,:]〗^* V ̅[i,:,:]≈I,i=1,…,N_samp
3)评价准则：为综合衡量SVD重构精度和左右奇异矩阵正交性约束，我们定义第i个采样点位上的SVD近似误差（Approximation Error, AE）为：
〖AE〗_i=‖H ̅_label [i,:,:]-U ̅[i,:,:]Σ〖[i,:,:] V ̅[i,:,:]〗^* ‖_F/‖H ̅_label [i,:,:]‖_F +‖〖U ̅[i,:,:]〗^* U ̅[i,:,:]-I‖_F+‖〖V ̅[i,:,:]〗^* V ̅[i,:,:]-I‖_F

最终，将所有点位的〖AE〗_i求平均得到该点位的整体近似误差，然后再对所有场景所有采样点位的误差求平均得到算法的最终误差。除了SVD近似误差这个性能指标外，我们还会同时评估模型前传一次所需的乘加次数作为模型效率指标。最后各个参赛队伍按SVD近似误差和乘加次数作为评价准则进行综合排名。


<三>数据说明
组委会将提供1组DebugData和3组CompetitionData，其中DebugData用于代码调试，3组CompetitionData为比赛数据，将分阶段提供（现在只有第1组）。详情如下：
DebugData，用于各个队伍调试代码。提供文件如下：配置文件：Round0CfgDataX.txt；模型测试文件：Round0TestDataX.npy，注意，X表示场景的编号，不同场景间数据没有必然联系，下同。
CompetitionData1，用于初赛第一轮（进32强）。提供文件如下：配置文件：Round1CfgDataX.txt；模型训练文件：Round1TrainDataX.npy，Round1TrainLabelX.npy；模型测试文件：Round1TestDataX.npy

提供数据文件格式说明
(1)RoundYCfgDataX.txt文件：第Y轮的第X个场景的配置参数文件。文件共有4行，每一行的定义的参数如下表所示：
N_sample，该场景下总采样点数，20000
M，信道矩阵行数，64
N，信道矩阵列数，64
Q，实虚部，2
R，目标为计算前R个最大的奇异值，32
(2)RoundYTrainDataX.npy文件：第Y轮的第X个场景中对应的非理想信道训练数据，用作模型训练输入。信道数据结构为4维复数张量，维度为N_sample×M×N×Q,其中各个维度的定义同之前。
(3)RoundYTrainLabelX.npy文件：第Y轮的第X个场景中对应的理想信道训练标签数据。数据结构与RoundYTrainDataX.npy相同。
(4)RoundYTestDataX.npy文件：第Y轮的第X个场景中对应的非理想信道测试数据，用作模型测试输入。数据结构与RoundYTrainDataX.npy相同。

模型训练完成后，参赛队伍需对每个测试场景 X，读取组委会提供的 RoundYTestDataX.npy 数据作为模型输入，并将模型输出结果保存为 RoundYTestOutputX.npz 文件。该文件包含以下三个张量数据：
U_out,所有采样点位信道矩阵对应的左奇异矩阵,N_samp×M×R×Q
S_out,所有采样点位信道矩阵对应的奇异值,N_samp×R
V_out,所有采样点位信道矩阵对应的右奇异矩阵,N_samp×N×R×Q

<四>其他说明
本赛题主要探索AI算法能力，限定使用神经网络算法。信道数据集中引入了复高斯白噪声、定时提前等非理想因素，传统 SVD 方法在此条件下性能较差，需通过神经网络学习实现更精确更鲁棒的重构。

本赛题开发代码语言限制为Python，设计网络模型限制使用标准包，如torch.nn中定义的常规网络结构，禁止使用复杂的复合算子和高相关算子，如SVD和EVD等。