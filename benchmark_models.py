#!/usr/bin/env python3
"""
模型性能基准测试脚本
对比轻量级模型和标准模型的FLOPs、参数量、推理速度和精度
"""

import os
import time
import torch
import numpy as np
from solution import SVDNet, LightweightSVDNet, count_model_flops, count_lightweight_model_flops
from utils import read_cfg_file, compute_approximation_error


def count_parameters(model):
    """计算模型参数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def benchmark_inference_speed(model, input_shape, device='cpu', num_runs=100):
    """基准测试推理速度"""
    model.eval()
    model = model.to(device)
    
    # 预热
    dummy_input = torch.randn(*input_shape).to(device)
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    if device == 'cuda':
        torch.cuda.synchronize()
    
    # 计时
    start_time = time.time()
    with torch.no_grad():
        for _ in range(num_runs):
            _ = model(dummy_input)
    
    if device == 'cuda':
        torch.cuda.synchronize()
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_runs
    
    return avg_time * 1000  # 返回毫秒


def test_model_accuracy(model, test_data, test_labels, device='cpu'):
    """测试模型精度"""
    model.eval()
    model = model.to(device)
    
    total_ae = 0
    num_samples = min(100, len(test_data))  # 测试前100个样本
    
    with torch.no_grad():
        for i in range(num_samples):
            data = torch.tensor(test_data[i:i+1], dtype=torch.float32).to(device)
            label = torch.tensor(test_labels[i:i+1], dtype=torch.float32).to(device)
            
            U, s, V = model(data)
            ae = compute_approximation_error(U, s, V, label)
            total_ae += ae
    
    return total_ae / num_samples


def main():
    """主函数"""
    print("🔍 模型性能基准测试")
    print("="*60)
    
    # 设备选择
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 读取配置
    data_dir = "CompetitionData1"
    cfg_path = os.path.join(data_dir, "Round1CfgData1.txt")
    
    if not os.path.exists(cfg_path):
        print(f"❌ 配置文件不存在: {cfg_path}")
        return
    
    _, M, N, _, R = read_cfg_file(cfg_path)
    print(f"模型配置: M={M}, N={N}, R={R}")
    
    # 创建模型
    print("\n📊 创建模型...")
    lightweight_model = LightweightSVDNet(M, N, R)
    standard_model = SVDNet(M, N, R)
    
    # 计算FLOPs
    lightweight_flops = count_lightweight_model_flops(M, N, R)
    standard_flops = count_model_flops(M, N, R)
    
    # 计算参数量
    lightweight_params = count_parameters(lightweight_model)
    standard_params = count_parameters(standard_model)
    
    # 打印模型对比
    print("\n📈 模型对比结果:")
    print("-" * 60)
    print(f"{'指标':<20} {'轻量级模型':<15} {'标准模型':<15} {'改进比例':<10}")
    print("-" * 60)
    print(f"{'FLOPs':<20} {lightweight_flops/1e6:.2f}M{'':<7} {standard_flops/1e6:.2f}M{'':<7} {standard_flops/lightweight_flops:.1f}x")
    print(f"{'参数量':<20} {lightweight_params/1e3:.1f}K{'':<8} {standard_params/1e3:.1f}K{'':<8} {standard_params/lightweight_params:.1f}x")
    
    # 推理速度测试
    print("\n⏱️  推理速度测试...")
    input_shape = (1, M, N, 2)  # 单个样本
    
    lightweight_time = benchmark_inference_speed(lightweight_model, input_shape, device)
    standard_time = benchmark_inference_speed(standard_model, input_shape, device)
    
    print(f"{'推理时间':<20} {lightweight_time:.2f}ms{'':<8} {standard_time:.2f}ms{'':<8} {standard_time/lightweight_time:.1f}x")
    
    # 精度测试（如果有测试数据）
    test_data_path = os.path.join(data_dir, "Round1TestData1.npy")
    test_label_path = os.path.join(data_dir, "Round1TrainLabel1.npy")  # 使用训练标签作为参考
    
    if os.path.exists(test_data_path) and os.path.exists(test_label_path):
        print("\n🎯 精度测试...")
        test_data = np.load(test_data_path).astype(np.float32)
        test_labels = np.load(test_label_path).astype(np.float32)
        
        lightweight_ae = test_model_accuracy(lightweight_model, test_data, test_labels, device)
        standard_ae = test_model_accuracy(standard_model, test_data, test_labels, device)
        
        print(f"{'近似误差(AE)':<20} {lightweight_ae:.6f}{'':<6} {standard_ae:.6f}{'':<6} {lightweight_ae/standard_ae:.2f}x")
    
    # 总结
    print("\n" + "="*60)
    print("📋 总结:")
    print(f"✅ 轻量级模型FLOPs减少: {(1-lightweight_flops/standard_flops)*100:.1f}%")
    print(f"✅ 轻量级模型参数减少: {(1-lightweight_params/standard_params)*100:.1f}%")
    print(f"✅ 轻量级模型推理加速: {standard_time/lightweight_time:.1f}x")
    
    # 建议
    print("\n💡 建议:")
    if lightweight_flops < standard_flops * 0.5:
        print("🎉 轻量级模型显著减少了计算复杂度，适合竞赛提交！")
    else:
        print("⚠️  可以进一步优化轻量级模型的FLOPs")
    
    if lightweight_time < standard_time * 0.7:
        print("🚀 轻量级模型推理速度显著提升！")
    else:
        print("🔧 可以进一步优化推理速度")


if __name__ == "__main__":
    main()
