import os
import numpy as np
import torch
import torch.nn.functional as F
from typing import Tuple, List, Dict, Optional


def read_cfg_file(file_path: str) -> Tuple[int, int, int, int, int]:
    """
    读取配置文件函数
    
    Args:
        file_path: 配置文件路径
        
    Returns:
        (samp_num, M, N, IQ, R): 配置参数元组
    """
    with open(file_path, 'r') as file:
        lines = file.readlines()
        samp_num = int(lines[0].strip())
        M = int(lines[1].strip())
        N = int(lines[2].strip())
        IQ = int(lines[3].strip())
        R = int(lines[4].strip())
    return samp_num, M, N, IQ, R


def get_data_paths(round_idx: int = 1) -> Tuple[str, str]:
    """
    获取数据路径配置
    
    Args:
        round_idx: 轮次索引
        
    Returns:
        (data_path, prefix): 数据路径和前缀
    """
    PathSet = {
        0: "DebugData",
        1: "CompetitionData1",
        2: "CompetitionData2",
        3: "CompetitionData3"
    }
    PrefixSet = {
        0: "Round0", 
        1: "Round1", 
        2: "Round2", 
        3: "Round3"
    }
    
    return PathSet[round_idx], PrefixSet[round_idx]


def find_case_indices(data_path: str) -> List[str]:
    """
    查找数据目录中的所有场景索引
    
    Args:
        data_path: 数据目录路径
        
    Returns:
        场景索引列表
    """
    if not os.path.exists(data_path):
        return []
        
    files = os.listdir(data_path)
    case_indices = []
    
    for f in sorted(files):
        if f.find('CfgData') != -1 and f.endswith('.txt'):
            case_indices.append(f.split('CfgData')[-1].split('.txt')[0])
    
    return case_indices


def complex_matmul(a: torch.Tensor, b: torch.Tensor) -> torch.Tensor:
    """
    复数矩阵乘法
    
    Args:
        a, b: 复数矩阵，最后一维为[real, imag]
        
    Returns:
        复数矩阵乘法结果
    """
    real_part = torch.matmul(a[..., 0], b[..., 0]) - torch.matmul(a[..., 1], b[..., 1])
    imag_part = torch.matmul(a[..., 0], b[..., 1]) + torch.matmul(a[..., 1], b[..., 0])
    return torch.stack([real_part, imag_part], dim=-1)


def complex_transpose_conj(x: torch.Tensor) -> torch.Tensor:
    """
    复数矩阵的共轭转置
    
    Args:
        x: 复数矩阵，最后一维为[real, imag]
        
    Returns:
        共轭转置结果
    """
    x_conj = torch.stack([x[..., 0], -x[..., 1]], dim=-1)
    return x_conj.transpose(-3, -2)


def compute_approximation_error(U: torch.Tensor, s: torch.Tensor, V: torch.Tensor, 
                               H_label: torch.Tensor) -> float:
    """
    计算SVD近似误差 (AE)
    
    Args:
        U: 左奇异矩阵 [batch, M, R, 2]
        s: 奇异值 [batch, R]
        V: 右奇异矩阵 [batch, N, R, 2]
        H_label: 标签信道矩阵 [batch, M, N, 2]
        
    Returns:
        平均近似误差
    """
    batch_size = U.shape[0]
    
    # 构造对角奇异值矩阵
    Sigma = torch.zeros(batch_size, U.shape[2], V.shape[2], 2, device=U.device)
    for i in range(min(U.shape[2], V.shape[2])):
        Sigma[:, i, i, 0] = s[:, i]
    
    # 重构信道矩阵: H_recon = U * Sigma * V^H
    US = complex_matmul(U, Sigma)
    VH = complex_transpose_conj(V)
    H_recon = complex_matmul(US, VH)
    
    # 计算重构误差
    H_diff = H_recon - H_label
    recon_error = torch.norm(H_diff.view(batch_size, -1), dim=1) / torch.norm(H_label.view(batch_size, -1), dim=1)
    
    # 计算正交性误差
    UH = complex_transpose_conj(U)
    UHU = complex_matmul(UH, U)
    I_U = torch.zeros_like(UHU)
    for i in range(U.shape[2]):
        I_U[:, i, i, 0] = 1.0
    
    VH = complex_transpose_conj(V)
    VHV = complex_matmul(VH, V)
    I_V = torch.zeros_like(VHV)
    for i in range(V.shape[2]):
        I_V[:, i, i, 0] = 1.0
    
    ortho_error_U = torch.norm((UHU - I_U).view(batch_size, -1), dim=1)
    ortho_error_V = torch.norm((VHV - I_V).view(batch_size, -1), dim=1)
    
    # 总误差
    total_error = recon_error + ortho_error_U + ortho_error_V
    
    return total_error.mean().item()


def count_model_flops(M: int, N: int, R: int) -> int:
    """
    计算模型的FLOPs
    
    Args:
        M, N, R: 模型参数
        
    Returns:
        总FLOPs数量
    """
    # 特征提取网络FLOPs
    conv1_flops = 2 * 3 * 3 * 32 * M * N  # Conv2d(2, 32, 3x3)
    conv2_flops = 32 * 3 * 3 * 64 * M * N  # Conv2d(32, 64, 3x3)
    pool_flops = 64 * 4 * 4  # AdaptiveAvgPool2d
    
    feature_dim = 64 * 4 * 4
    
    # 分支网络FLOPs
    u_branch_flops = feature_dim * 256 + 256 * (M * R * 2)  # UnitaryBranch for U
    s_branch_flops = feature_dim * 256 + 256 * R  # SingularValueBranch
    v_branch_flops = feature_dim * 256 + 256 * (N * R * 2)  # UnitaryBranch for V
    
    # 正交化FLOPs (Gram-Schmidt)
    ortho_u_flops = M * R * R * 8  # 复数Gram-Schmidt for U
    ortho_v_flops = N * R * R * 8  # 复数Gram-Schmidt for V
    
    total_flops = (conv1_flops + conv2_flops + pool_flops + 
                   u_branch_flops + s_branch_flops + v_branch_flops + 
                   ortho_u_flops + ortho_v_flops)
    
    return total_flops


def load_model_safely(model: torch.nn.Module, model_paths: List[str], 
                     device: str = 'cpu') -> bool:
    """
    安全加载模型参数
    
    Args:
        model: 模型实例
        model_paths: 模型文件路径列表
        device: 设备
        
    Returns:
        是否成功加载
    """
    for model_path in model_paths:
        if os.path.exists(model_path):
            try:
                print(f"正在加载模型: {model_path}")
                checkpoint = torch.load(model_path, map_location=device)
                
                if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['model_state_dict'])
                    if 'epoch' in checkpoint:
                        print(f"模型加载成功，来自epoch {checkpoint['epoch']}")
                    else:
                        print("模型加载成功")
                else:
                    model.load_state_dict(checkpoint)
                    print("模型加载成功")
                
                return True
            except Exception as e:
                print(f"加载模型失败 {model_path}: {e}")
                continue
    
    print("警告: 未找到可用的训练模型，使用随机初始化")
    return False


def save_test_results(U_out: np.ndarray, S_out: np.ndarray, V_out: np.ndarray,
                     prefix: str, case_idx: str) -> str:
    """
    保存测试结果
    
    Args:
        U_out, S_out, V_out: 输出矩阵
        prefix: 文件前缀
        case_idx: 场景索引
        
    Returns:
        保存的文件名
    """
    # 确保数据类型正确
    U_out = U_out.astype(np.float32)
    S_out = S_out.astype(np.float32)
    V_out = V_out.astype(np.float32)
    
    # 保存文件
    output_file = f"{prefix}TestOutput{case_idx}.npz"
    np.savez(output_file, U_out=U_out, S_out=S_out, V_out=V_out)
    
    print(f"结果已保存到: {output_file}")
    return output_file


def create_identity_matrix(batch_size: int, dim: int, device: str) -> torch.Tensor:
    """
    创建复数单位矩阵
    
    Args:
        batch_size: 批次大小
        dim: 矩阵维度
        device: 设备
        
    Returns:
        复数单位矩阵 [batch_size, dim, dim, 2]
    """
    I = torch.zeros(batch_size, dim, dim, 2, device=device)
    for i in range(dim):
        I[:, i, i, 0] = 1.0
    return I
