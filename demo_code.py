import os
import sys
import time
import numpy as np
import torch
from typing import List, Optional

# 导入优化模块
from solution import SVDNet
from optimized_inference import create_optimized_inference_pipeline
from utils import read_cfg_file, get_data_paths, find_case_indices


class CompetitionSubmission:
    """竞赛提交类 - 统一接口"""
    
    def __init__(self, model_path: str = 'best_model.pth', 
                 device: str = 'auto', batch_size: int = 64):
        """
        初始化竞赛提交系统
        
        Args:
            model_path: 模型文件路径
            device: 计算设备 ('auto', 'cpu', 'cuda')
            batch_size: 批次大小
        """
        # 自动选择设备
        if device == 'auto':
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        self.device = device
        self.batch_size = batch_size
        
        print(f"竞赛提交系统初始化")
        print(f"设备: {device}")
        print(f"批次大小: {batch_size}")
        
        # 创建优化推理管道
        self.inference_optimizer = create_optimized_inference_pipeline(
            model_path, device, batch_size
        )
        
        print("推理管道初始化完成")
    
    def process_round(self, round_idx: int = 1, 
                     output_dir: str = '.') -> List[str]:
        """
        处理指定轮次的所有场景
        
        Args:
            round_idx: 轮次索引
            output_dir: 输出目录
            
        Returns:
            生成的输出文件列表
        """
        print(f"\n{'='*60}")
        print(f"处理 Round {round_idx}")
        print(f"{'='*60}")
        
        # 获取数据路径
        data_path, prefix = get_data_paths(round_idx)
        
        if not os.path.exists(data_path):
            print(f"错误：数据目录 {data_path} 不存在！")
            return []
        
        # 查找所有场景
        case_indices = find_case_indices(data_path)
        if not case_indices:
            print(f"错误：在 {data_path} 中未找到配置文件！")
            return []
        
        print(f"找到 {len(case_indices)} 个场景: {case_indices}")
        
        output_files = []
        total_start_time = time.time()
        
        # 处理每个场景
        for i, case_idx in enumerate(case_indices):
            print(f"\n[{i+1}/{len(case_indices)}] 处理场景 {case_idx}")
            
            try:
                output_file = self._process_single_case(
                    data_path, prefix, case_idx, output_dir
                )
                if output_file:
                    output_files.append(output_file)
            except Exception as e:
                print(f"处理场景 {case_idx} 时出错: {e}")
                continue
        
        total_time = time.time() - total_start_time
        print(f"\n{'='*60}")
        print(f"Round {round_idx} 处理完成")
        print(f"总耗时: {total_time:.2f}s")
        print(f"生成文件: {len(output_files)} 个")
        print(f"{'='*60}")
        
        return output_files
    
    def _process_single_case(self, data_path: str, prefix: str, 
                           case_idx: str, output_dir: str) -> Optional[str]:
        """处理单个场景"""
        case_start_time = time.time()
        
        # 读取配置文件
        cfg_path = os.path.join(data_path, f"{prefix}CfgData{case_idx}.txt")
        try:
            samp_num, M, N, IQ, R = read_cfg_file(cfg_path)
            print(f"  配置: 样本={samp_num}, M={M}, N={N}, R={R}")
        except Exception as e:
            print(f"  错误：读取配置文件失败 - {e}")
            return None
        
        # 读取测试数据
        test_data_file = os.path.join(data_path, f"{prefix}TestData{case_idx}.npy")
        if not os.path.exists(test_data_file):
            print(f"  错误：测试数据文件不存在 - {test_data_file}")
            return None
        
        try:
            H_data_all = np.load(test_data_file).astype(np.float32)
            print(f"  数据形状: {H_data_all.shape}")
            
            if (M, N, IQ) != H_data_all.shape[1:4]:
                print(f"  错误：数据维度不匹配！期望({M},{N},{IQ})，实际{H_data_all.shape[1:4]}")
                return None
                
        except Exception as e:
            print(f"  错误：加载测试数据失败 - {e}")
            return None
        
        # 执行推理
        try:
            output_file = self.inference_optimizer.optimize_for_submission(
                H_data_all, prefix, case_idx
            )
            
            # 移动到输出目录
            if output_dir != '.' and output_dir != '':
                os.makedirs(output_dir, exist_ok=True)
                new_path = os.path.join(output_dir, os.path.basename(output_file))
                if new_path != output_file:
                    os.rename(output_file, new_path)
                    output_file = new_path
            
            case_time = time.time() - case_start_time
            print(f"  场景 {case_idx} 完成，耗时: {case_time:.2f}s")
            
            return output_file
            
        except Exception as e:
            print(f"  错误：推理失败 - {e}")
            return None
    
    def validate_outputs(self, output_files: List[str]) -> bool:
        """验证输出文件"""
        print(f"\n验证输出文件...")
        
        all_valid = True
        for output_file in output_files:
            try:
                data = np.load(output_file)
                required_keys = ['U_out', 'S_out', 'V_out']
                
                if not all(key in data for key in required_keys):
                    print(f"  ❌ {output_file}: 缺少必要的键")
                    all_valid = False
                    continue
                
                U_out = data['U_out']
                S_out = data['S_out']
                V_out = data['V_out']
                
                # 检查形状
                if len(U_out.shape) != 4 or len(S_out.shape) != 2 or len(V_out.shape) != 4:
                    print(f"  ❌ {output_file}: 数据形状错误")
                    all_valid = False
                    continue
                
                # 检查数据类型
                if not all(arr.dtype == np.float32 for arr in [U_out, S_out, V_out]):
                    print(f"  ❌ {output_file}: 数据类型错误")
                    all_valid = False
                    continue
                
                print(f"  ✅ {output_file}: 验证通过")
                
            except Exception as e:
                print(f"  ❌ {output_file}: 验证失败 - {e}")
                all_valid = False
        
        if all_valid:
            print("所有输出文件验证通过！")
        else:
            print("部分输出文件验证失败！")
        
        return all_valid
    



def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='竞赛提交脚本')
    parser.add_argument('--round', type=int, default=1, help='轮次索引')
    parser.add_argument('--model', type=str, default='best_model.pth', help='模型路径')
    parser.add_argument('--device', type=str, default='auto', help='计算设备')
    parser.add_argument('--batch-size', type=int, default=64, help='批次大小')
    parser.add_argument('--output-dir', type=str, default='.', help='输出目录')
    
    args = parser.parse_args()

    # 创建提交系统
    try:
        submission = CompetitionSubmission(
            model_path=args.model,
            device=args.device,
            batch_size=args.batch_size
        )
    except Exception as e:
        print(f"初始化失败: {e}")
        return
    
    # 处理指定轮次
    output_files = submission.process_round(args.round, args.output_dir)
    
    if not output_files:
        print("没有生成任何输出文件！")
        return
    
    # 验证输出
    if submission.validate_outputs(output_files):
        print("\n🎉 所有文件生成成功并验证通过！")
    else:
        print("\n⚠️  部分文件验证失败，请检查！")


if __name__ == "__main__":
    main()
