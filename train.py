
import os
import gc
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split
from typing import List, Tuple, Dict
from solution import SVDNet, LightweightSVDNet, count_model_flops, count_lightweight_model_flops, svd_loss
import math

from utils import complex_matmul, complex_transpose_conj, compute_approximation_error


class ChannelDataset(Dataset):
    """信道数据集类 - 支持数据增强"""
    def __init__(self, data_files: List[str], label_files: List[str], 
                 max_samples_per_file: int = 2000, augment: bool = True):
        self.data = []
        self.labels = []
        self.augment = augment
        
        for data_file, label_file in zip(data_files, label_files):
            if os.path.exists(data_file) and os.path.exists(label_file):
                data = np.load(data_file).astype(np.float32)
                label = np.load(label_file).astype(np.float32)
                
                # 限制样本数量
                if len(data) > max_samples_per_file:
                    indices = np.random.choice(len(data), max_samples_per_file, replace=False)
                    data = data[indices]
                    label = label[indices]
                
                self.data.append(data)
                self.labels.append(label)
        
        if self.data:
            self.data = np.concatenate(self.data, axis=0)
            self.labels = np.concatenate(self.labels, axis=0)
        else:
            raise ValueError("没有找到有效的数据文件！")
        
        print(f"加载数据: {len(self.data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        data = torch.tensor(self.data[idx], dtype=torch.float32)
        label = torch.tensor(self.labels[idx], dtype=torch.float32)
        
        # 数据增强
        if self.augment and np.random.random() < 0.3:
            data, label = self._augment_data(data, label)
        
        return data, label
    
    def _augment_data(self, data, label):
        """数据增强方法"""
        # 1. 添加小量噪声
        if np.random.random() < 0.5:
            noise_std = 0.01 * torch.std(data)
            noise = torch.randn_like(data) * noise_std
            data = data + noise
            label = label + noise
        
        # 2. 随机缩放
        if np.random.random() < 0.3:
            scale = 0.9 + 0.2 * torch.rand(1)  # [0.9, 1.1]
            data = data * scale
            label = label * scale
        
        # 3. 相位旋转 (对复数数据)
        if np.random.random() < 0.3:
            angle = (torch.rand(1) - 0.5) * 0.2  # [-0.1, 0.1] 弧度
            cos_a, sin_a = torch.cos(angle), torch.sin(angle)
            
            # 应用旋转矩阵
            real_part = data[..., 0] * cos_a - data[..., 1] * sin_a
            imag_part = data[..., 0] * sin_a + data[..., 1] * cos_a
            data = torch.stack([real_part, imag_part], dim=-1)
            
            real_part = label[..., 0] * cos_a - label[..., 1] * sin_a
            imag_part = label[..., 0] * sin_a + label[..., 1] * cos_a
            label = torch.stack([real_part, imag_part], dim=-1)
        
        return data, label


class SVDTrainer:
    """改进的SVD训练器"""
    def __init__(self, model, device='cuda', config=None):
        self.model = model.to(device)
        self.device = device
        self.config = config or self._default_config()
        
        # 优化器 - 使用AdamW + 学习率预热
        self.optimizer = optim.AdamW(
            model.parameters(), 
            lr=self.config['lr'],
            weight_decay=self.config['weight_decay'],
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器 - 余弦退火 + 预热
        self.warmup_epochs = self.config['warmup_epochs']
        self.total_epochs = self.config['total_epochs']
        
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, 
            T_max=self.total_epochs - self.warmup_epochs,
            eta_min=self.config['lr'] * 0.01
        )
        
        # 训练状态
        self.current_epoch = 0
        self.best_ae = float('inf')
        self.patience_counter = 0
    
    def _default_config(self):
        """默认配置"""
        return {
            'lr': 2e-3,
            'weight_decay': 1e-4,
            'warmup_epochs': 5,
            'total_epochs': 50,
            'patience': 10,
            'lambda_ortho_start': 0.1,
            'lambda_ortho_end': 1.0,
            'lambda_recon': 1.0,
            'lambda_singular': 0.1,
            'lambda_smooth': 0.05
        }
    
    def _warmup_lr(self, epoch):
        """学习率预热"""
        if epoch < self.warmup_epochs:
            lr_scale = (epoch + 1) / self.warmup_epochs
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = self.config['lr'] * lr_scale
    
    def _get_loss_weights(self, epoch):
        """动态调整损失权重"""
        # 正交性约束权重逐渐增加
        progress = min(1.0, epoch / (self.total_epochs * 0.6))
        lambda_ortho = (self.config['lambda_ortho_start'] + 
                       (self.config['lambda_ortho_end'] - self.config['lambda_ortho_start']) * progress)
        
        return {
            'lambda_ortho': lambda_ortho,
            'lambda_recon': self.config['lambda_recon'],
            'lambda_singular': self.config['lambda_singular'],
            'lambda_smooth': self.config['lambda_smooth']
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_recon_loss = 0
        total_ortho_loss = 0
        
        # 学习率预热
        self._warmup_lr(self.current_epoch)
        
        # 获取当前epoch的损失权重
        loss_weights = self._get_loss_weights(self.current_epoch)
        
        for batch_idx, (data, label) in enumerate(train_loader):
            data, label = data.to(self.device), label.to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            U, s, V = self.model(data)
            
            # 计算损失
            loss, recon_loss, ortho_loss_U, ortho_loss_V = svd_loss(
                U, s, V, label, **loss_weights
            )
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_ortho_loss += (ortho_loss_U.item() + ortho_loss_V.item())
            
            # 打印进度
            if batch_idx % 50 == 0:
                print(f'Batch {batch_idx}, Loss: {loss.item():.6f}, '
                      f'Recon: {recon_loss.item():.6f}, '
                      f'Ortho: {(ortho_loss_U.item() + ortho_loss_V.item()):.6f}')
            
            # 内存清理
            del U, s, V, loss, recon_loss, ortho_loss_U, ortho_loss_V
            if batch_idx % 100 == 0:
                torch.cuda.empty_cache() if torch.cuda.is_available() else gc.collect()
        
        # 更新学习率 (预热期后)
        if self.current_epoch >= self.warmup_epochs:
            self.scheduler.step()
        
        self.current_epoch += 1
        
        return (total_loss / len(train_loader),
                total_recon_loss / len(train_loader),
                total_ortho_loss / len(train_loader))
    
    def validate(self, val_loader):
        """验证"""
        self.model.eval()
        total_ae = 0
        total_samples = 0
        
        with torch.no_grad():
            for data, label in val_loader:
                data, label = data.to(self.device), label.to(self.device)
                
                U, s, V = self.model(data)
                ae = compute_approximation_error(U, s, V, label)
                
                total_ae += ae * data.shape[0]
                total_samples += data.shape[0]
                
                del U, s, V
        
        return total_ae / total_samples
    
    def save_checkpoint(self, filepath, epoch, ae):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_ae': ae,
            'config': self.config
        }
        torch.save(checkpoint, filepath)


def train_model(data_dir: str, round_num: int = 1, scene_ids: List[int] = [1, 2, 3],
                        max_samples_per_scene: int = 2000, config: Dict = None,
                        use_lightweight: bool = True):
    """训练模型 - 支持轻量级和标准模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 读取配置
    from utils import read_cfg_file
    cfg_path = os.path.join(data_dir, f'Round{round_num}CfgData{scene_ids[0]}.txt')
    _, M, N, _, R = read_cfg_file(cfg_path)
    print(f"模型参数: M={M}, N={N}, R={R}")

    # 选择模型和计算FLOPs
    if use_lightweight:
        model = LightweightSVDNet(M, N, R)
        total_flops = count_lightweight_model_flops(M, N, R)
        model_name = "轻量级模型"
        save_path = 'best_lightweight_model.pth'
    else:
        model = SVDNet(M, N, R)
        total_flops = count_model_flops(M, N, R)
        model_name = "标准模型"
        save_path = 'best_model.pth'

    print(f"{model_name}FLOPs: {total_flops:,} ({total_flops/1e6:.2f}M)")

    # 准备数据
    data_files = [os.path.join(data_dir, f'Round{round_num}TrainData{i}.npy') for i in scene_ids]
    label_files = [os.path.join(data_dir, f'Round{round_num}TrainLabel{i}.npy') for i in scene_ids]

    # 创建数据集
    dataset = ChannelDataset(data_files, label_files, max_samples_per_scene, augment=True)

    # 划分训练集和验证集
    train_size = int(0.9 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    # 创建数据加载器
    batch_size = 64 if use_lightweight else 32  # 轻量级模型可以用更大batch
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size*2, shuffle=False, num_workers=2)

    print(f"训练样本: {len(train_dataset)}, 验证样本: {len(val_dataset)}")

    # 创建训练器
    trainer = SVDTrainer(model, device, config)

    # 训练循环
    best_ae = float('inf')
    patience_counter = 0
    patience = trainer.config['patience']

    print(f"开始训练{model_name}...")

    for epoch in range(trainer.config['total_epochs']):
        print(f"\nEpoch {epoch + 1}/{trainer.config['total_epochs']}")

        # 训练
        train_loss, train_recon, train_ortho = trainer.train_epoch(train_loader)

        # 验证
        val_ae = trainer.validate(val_loader)

        print(f"Train Loss: {train_loss:.6f}, Train Recon: {train_recon:.6f}, "
              f"Train Ortho: {train_ortho:.6f}")
        print(f"Val AE: {val_ae:.6f}")
        print(f"当前学习率: {trainer.optimizer.param_groups[0]['lr']:.6f}")

        # 保存最佳模型
        if val_ae < best_ae:
            best_ae = val_ae
            patience_counter = 0
            trainer.save_checkpoint(save_path, epoch, val_ae)
            print(f"保存新的最佳{model_name}，AE: {best_ae:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print("早停触发")
                break

        # 内存清理
        torch.cuda.empty_cache() if torch.cuda.is_available() else gc.collect()

    print(f"{model_name}训练完成！最佳AE: {best_ae:.6f}")
    return model


if __name__ == "__main__":
    # 增强轻量级模型训练配置 - 专注降低AE
    lightweight_config = {
        'lr': 5e-3,  # 更高学习率，快速收敛
        'weight_decay': 1e-5,  # 极小正则化，避免欠拟合
        'warmup_epochs': 2,
        'total_epochs': 50,  # 增加训练轮次
        'patience': 12,  # 增加耐心，避免早停
        'lambda_ortho_start': 0.02,  # 极温和的正交性约束开始
        'lambda_ortho_end': 0.5,  # 适中的正交性约束结束
        'lambda_recon': 2.0,  # 加强重构损失权重
        'lambda_singular': 0.05,  # 减少奇异值约束
        'lambda_smooth': 0.01  # 减少平滑性约束
    }

    # 标准模型训练配置
    standard_config = {
        'lr': 2e-3,
        'weight_decay': 1e-4,
        'warmup_epochs': 5,
        'total_epochs': 40,
        'patience': 8,
        'lambda_ortho_start': 0.1,
        'lambda_ortho_end': 1.2,
        'lambda_recon': 1.0,
        'lambda_singular': 0.15,
        'lambda_smooth': 0.03
    }

    data_dir = "CompetitionData1"
    if os.path.exists(data_dir):
        print("🚀 开始训练轻量级模型 - 专为竞赛优化！")
        print("="*60)

        # 训练轻量级模型
        lightweight_model = train_model(data_dir, config=lightweight_config, use_lightweight=True)

        print("\n" + "="*60)
        print("✅ 轻量级模型训练完成！")

        # 可选：同时训练标准模型进行对比
        train_standard = input("\n是否同时训练标准模型进行对比？(y/n): ").lower() == 'y'
        if train_standard:
            print("\n🔄 开始训练标准模型进行对比...")
            standard_model = train_model(data_dir, config=standard_config, use_lightweight=False)
            print("✅ 标准模型训练完成！")
    else:
        print(f"❌ 数据目录 {data_dir} 不存在！")
