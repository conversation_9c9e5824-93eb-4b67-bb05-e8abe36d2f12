import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Optional
import gc

from utils import save_test_results


class InferenceOptimizer:
    """推理优化器"""
    
    def __init__(self, model: nn.Module, device: str = 'cpu', 
                 batch_size: int = 64, use_amp: bool = False):
        self.model = model.to(device)
        self.device = device
        self.batch_size = batch_size
        self.use_amp = False  # 禁用AMP，避免兼容性问题
        
        # 设置为评估模式
        self.model.eval()
        
        # 预热模型
        self._warmup_model()
    
    def _warmup_model(self):
        """模型预热 - 优化GPU内核"""
        if self.device == 'cuda':
            dummy_input = torch.randn(self.batch_size, 64, 64, 2).to(self.device)
            
            with torch.no_grad():
                for _ in range(5):
                    _ = self.model(dummy_input)
            
            torch.cuda.synchronize()
            torch.cuda.empty_cache()
    
    def batch_inference(self, data: np.ndarray, 
                       progress_callback: Optional[callable] = None) -> <PERSON><PERSON>[np.ndarray, np.ndarray, np.ndarray]:
        """
        批量推理
        
        Args:
            data: 输入数据 [N, M, N, 2]
            progress_callback: 进度回调函数
            
        Returns:
            (U_out, S_out, V_out): 输出结果
        """
        total_samples = data.shape[0]
        M, N = data.shape[1], data.shape[2]
        R = 32  # 假设R=32，实际应该从模型获取
        
        # 预分配输出数组
        U_out_all = np.zeros((total_samples, M, R, 2), dtype=np.float32)
        S_out_all = np.zeros((total_samples, R), dtype=np.float32)
        V_out_all = np.zeros((total_samples, N, R, 2), dtype=np.float32)
        
        # 批量处理
        num_batches = (total_samples + self.batch_size - 1) // self.batch_size
        
        with torch.no_grad():
            for batch_idx in range(num_batches):
                start_idx = batch_idx * self.batch_size
                end_idx = min(start_idx + self.batch_size, total_samples)
                
                # 获取批次数据
                batch_data = data[start_idx:end_idx]
                batch_tensor = torch.tensor(batch_data, dtype=torch.float32).to(self.device)
                
                # 推理
                U_batch, S_batch, V_batch = self.model(batch_tensor)
                
                # 保存结果
                U_out_all[start_idx:end_idx] = U_batch.cpu().numpy()
                S_out_all[start_idx:end_idx] = S_batch.cpu().numpy()
                V_out_all[start_idx:end_idx] = V_batch.cpu().numpy()
                
                # 内存清理
                del batch_tensor, U_batch, S_batch, V_batch
                if batch_idx % 10 == 0:
                    torch.cuda.empty_cache() if self.device == 'cuda' else gc.collect()
                
                # 进度回调
                if progress_callback:
                    progress = (batch_idx + 1) / num_batches
                    progress_callback(progress, batch_idx + 1, num_batches)
        
        return U_out_all, S_out_all, V_out_all
    
    def single_inference(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        单样本推理 - 用于兼容性
        
        Args:
            data: 单个样本 [M, N, 2]
            
        Returns:
            (U_out, S_out, V_out): 输出结果
        """
        # 添加批次维度
        batch_data = np.expand_dims(data, axis=0)
        
        # 批量推理
        U_batch, S_batch, V_batch = self.batch_inference(batch_data)
        
        # 移除批次维度
        return U_batch[0], S_batch[0], V_batch[0]
    
    def optimize_for_submission(self, data: np.ndarray, 
                               output_prefix: str, case_idx: str) -> str:
        """
        针对竞赛提交的优化推理
        
        Args:
            data: 输入数据
            output_prefix: 输出文件前缀
            case_idx: 场景索引
            
        Returns:
            输出文件名
        """
        print(f"开始优化推理 - 场景 {case_idx}")
        print(f"数据形状: {data.shape}")
        
        def progress_callback(progress, current_batch, total_batches):
            if current_batch % 50 == 0 or current_batch == total_batches:
                print(f"  推理进度: {progress*100:.1f}% ({current_batch}/{total_batches})")
        
        # 执行批量推理
        U_out, S_out, V_out = self.batch_inference(data, progress_callback)
        
        # 保存结果
        output_file = save_test_results(U_out, S_out, V_out, output_prefix, case_idx)
        
        print(f"场景 {case_idx} 推理完成")
        return output_file


class ModelFusion:
    """模型融合 - 提高精度"""
    
    def __init__(self, models: list, weights: Optional[list] = None, device: str = 'cpu'):
        self.models = [model.to(device).eval() for model in models]
        self.device = device
        self.weights = weights or [1.0 / len(models)] * len(models)
        
        assert len(self.models) == len(self.weights), "模型数量和权重数量不匹配"
        assert abs(sum(self.weights) - 1.0) < 1e-6, "权重和应该为1"
    
    def fused_inference(self, data: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        融合推理
        
        Args:
            data: 输入数据
            
        Returns:
            融合后的输出
        """
        U_list, S_list, V_list = [], [], []
        
        with torch.no_grad():
            for model in self.models:
                U, S, V = model(data)
                U_list.append(U)
                S_list.append(S)
                V_list.append(V)
        
        # 加权平均
        U_fused = sum(w * U for w, U in zip(self.weights, U_list))
        S_fused = sum(w * S for w, S in zip(self.weights, S_list))
        V_fused = sum(w * V for w, V in zip(self.weights, V_list))
        
        # 重新正交化U和V
        U_fused = self._reorthogonalize(U_fused)
        V_fused = self._reorthogonalize(V_fused)
        
        # 确保奇异值降序排列
        S_fused, indices = torch.sort(S_fused, dim=-1, descending=True)
        
        # 重新排列U和V的列
        batch_size = U_fused.shape[0]
        for b in range(batch_size):
            U_fused[b] = U_fused[b, :, indices[b]]
            V_fused[b] = V_fused[b, :, indices[b]]
        
        return U_fused, S_fused, V_fused
    
    def _reorthogonalize(self, matrix: torch.Tensor) -> torch.Tensor:
        """重新正交化矩阵"""
        # 简化的正交化 - 使用QR分解的思想但避免复杂算子
        _, _, dim2, _ = matrix.shape
        
        # 转换为复数
        matrix_complex = torch.complex(matrix[..., 0], matrix[..., 1])
        
        # 简单的Gram-Schmidt
        Q = torch.zeros_like(matrix_complex)
        
        for i in range(dim2):
            v = matrix_complex[:, :, i]
            
            for j in range(i):
                q_j = Q[:, :, j]
                proj_coeff = torch.sum(q_j.conj() * v, dim=1, keepdim=True)
                v = v - proj_coeff * q_j
            
            norm = torch.norm(v, dim=1, keepdim=True)
            norm = torch.clamp(norm, min=1e-8)
            Q[:, :, i] = v / norm
        
        # 转换回实虚部表示
        result = torch.stack([Q.real, Q.imag], dim=-1)
        return result


def create_optimized_inference_pipeline(model_path: str, device: str = 'cpu',
                                      batch_size: int = 64) -> InferenceOptimizer:
    """
    创建优化的推理管道

    Args:
        model_path: 模型文件路径
        device: 计算设备
        batch_size: 批次大小

    Returns:
        推理优化器
    """
    from solution import SVDNet

    # 加载模型 - 使用默认参数
    model = SVDNet()

    try:
        checkpoint = torch.load(model_path, map_location=device)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        print(f"成功加载模型: {model_path}")
    except Exception as e:
        print(f"加载模型失败: {e}")
        print("使用随机初始化的模型")

    # 创建推理优化器
    optimizer = InferenceOptimizer(model, device, batch_size, use_amp=False)

    return optimizer


def benchmark_inference_speed(model_path: str, device: str = 'cpu'):
    """推理速度基准测试"""
    import time
    
    print("开始推理速度基准测试...")
    
    # 创建优化器
    optimizer = create_optimized_inference_pipeline(model_path, device, batch_size=32)
    
    # 测试数据
    test_sizes = [100, 500, 1000, 5000]
    
    for size in test_sizes:
        dummy_data = np.random.randn(size, 64, 64, 2).astype(np.float32)
        
        # 预热
        if size <= 100:
            _ = optimizer.batch_inference(dummy_data[:10])
        
        # 计时
        start_time = time.time()
        _ = optimizer.batch_inference(dummy_data)
        end_time = time.time()
        
        total_time = end_time - start_time
        samples_per_second = size / total_time
        
        print(f"样本数: {size:4d}, 总时间: {total_time:.3f}s, "
              f"速度: {samples_per_second:.1f} samples/s")
    
    print("基准测试完成")


if __name__ == "__main__":
    # 示例用法
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 基准测试
    benchmark_inference_speed('best_model.pth', device)
